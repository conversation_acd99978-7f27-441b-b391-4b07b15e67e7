import connectDB from './mongodb';
import Course from '@/models/Course';
import Job from '@/models/Job';
import User from '@/models/User';
import Event from '@/models/Event';
import Resource from '@/models/Resource';
import MentorProfile from '@/models/Mentorship';
import bcrypt from 'bcryptjs';

export const sampleCourses = [
  {
    title: 'Introduction to Satellite Engineering',
    description: 'Learn the fundamentals of satellite design, orbital mechanics, and space systems engineering. This comprehensive course covers everything from basic concepts to advanced satellite subsystems.',
    instructor: 'Dr. <PERSON><PERSON>',
    instructorId: null, // Will be set when we create users
    category: 'Space Technology',
    level: 'beginner',
    duration: 40,
    thumbnail: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400',
    tags: ['satellite', 'engineering', 'space', 'orbital mechanics'],
    modules: [
      {
        title: 'Introduction to Space Systems',
        description: 'Overview of space systems and satellite fundamentals',
        order: 1,
        lessons: [
          {
            title: 'What is a Satellite?',
            type: 'video',
            content: 'https://example.com/video1',
            duration: 30,
            order: 1,
          },
          {
            title: 'Types of Satellites',
            type: 'text',
            content: 'Detailed explanation of different satellite types...',
            duration: 20,
            order: 2,
          },
        ],
      },
    ],
    prerequisites: ['Basic physics knowledge', 'Mathematics fundamentals'],
    learningOutcomes: ['Understand satellite basics', 'Design simple satellite systems'],
    certification: {
      available: true,
      passingScore: 80,
    },
    pricing: {
      isFree: true,
      price: 0,
    },
    enrollment: {
      totalStudents: 1250,
      isOpen: true,
    },
    ratings: {
      average: 4.8,
      totalRatings: 156,
    },
    isPublished: true,
  },
  {
    title: 'AI Applications in Space Exploration',
    description: 'Explore how artificial intelligence is revolutionizing space missions and data analysis. Learn to implement AI algorithms for space applications.',
    instructor: 'Prof. Kwame Asante',
    instructorId: null,
    category: 'AI in Space',
    level: 'intermediate',
    duration: 35,
    thumbnail: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400',
    tags: ['AI', 'machine learning', 'space exploration', 'data analysis'],
    modules: [
      {
        title: 'AI Fundamentals for Space',
        description: 'Introduction to AI concepts in space context',
        order: 1,
        lessons: [
          {
            title: 'Machine Learning Basics',
            type: 'video',
            content: 'https://example.com/video2',
            duration: 45,
            order: 1,
          },
        ],
      },
    ],
    prerequisites: ['Programming experience', 'Basic mathematics'],
    learningOutcomes: ['Implement AI algorithms', 'Analyze space data'],
    certification: {
      available: true,
      passingScore: 85,
    },
    pricing: {
      isFree: false,
      price: 99,
    },
    enrollment: {
      totalStudents: 890,
      isOpen: true,
    },
    ratings: {
      average: 4.9,
      totalRatings: 89,
    },
    isPublished: true,
  },
  {
    title: 'Cybersecurity for Space Systems',
    description: 'Understand the unique security challenges and solutions for space-based infrastructure. Learn to protect satellite systems from cyber threats.',
    instructor: 'Dr. Fatima Al-Rashid',
    instructorId: null,
    category: 'Cybersecurity',
    level: 'advanced',
    duration: 50,
    thumbnail: 'https://images.unsplash.com/photo-1563206767-5b18f218e8de?w=400',
    tags: ['cybersecurity', 'space systems', 'satellite security'],
    modules: [
      {
        title: 'Space Cybersecurity Fundamentals',
        description: 'Understanding security challenges in space',
        order: 1,
        lessons: [
          {
            title: 'Threat Landscape in Space',
            type: 'video',
            content: 'https://example.com/video3',
            duration: 40,
            order: 1,
          },
        ],
      },
    ],
    prerequisites: ['Cybersecurity basics', 'Network fundamentals'],
    learningOutcomes: ['Secure space systems', 'Implement security protocols'],
    certification: {
      available: true,
      passingScore: 90,
    },
    pricing: {
      isFree: false,
      price: 149,
    },
    enrollment: {
      totalStudents: 650,
      isOpen: true,
    },
    ratings: {
      average: 4.7,
      totalRatings: 67,
    },
    isPublished: true,
  },
];

export const sampleJobs = [
  // African Space Research Institute Jobs
  {
    title: 'Senior Satellite Systems Engineer',
    company: 'African Space Research Institute',
    employerId: null, // Will be set when we create users
    description: 'Lead the design and development of next-generation Earth observation satellites. Work with cutting-edge technology to advance Africa\'s space capabilities and mentor junior engineers in satellite subsystem design.',
    requirements: [
      'Master\'s degree in Aerospace/Electrical Engineering',
      '5+ years experience in satellite systems',
      'MATLAB/Simulink proficiency',
      'Experience with satellite subsystems (ADCS, Power, Communications)',
      'Knowledge of space environment and orbital mechanics'
    ],
    responsibilities: [
      'Lead satellite subsystem design and integration',
      'Conduct system-level analysis and verification',
      'Mentor junior engineers and researchers',
      'Collaborate with international space agencies',
      'Develop technical documentation and reports'
    ],
    type: 'full-time',
    location: {
      type: 'on-site',
      city: 'Nairobi',
      country: 'Kenya',
    },
    salary: {
      min: 85000,
      max: 125000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Satellite Engineering',
    tags: ['satellite', 'engineering', 'space systems', 'leadership'],
    skillsRequired: ['MATLAB', 'Simulink', 'Systems Engineering', 'ADCS', 'Power Systems'],
    experienceLevel: 'senior',
    education: ['Master\'s degree in Aerospace/Electrical Engineering'],
    benefits: ['Health insurance', 'Professional development', 'Research opportunities', 'Conference attendance'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 28,
      shortlisted: 6,
      interviewed: 3,
      hired: 0,
    },
  },
  {
    title: 'Space Technology Research Fellow',
    company: 'African Space Research Institute',
    employerId: null,
    description: 'Conduct cutting-edge research in space technology applications for African development. Focus on Earth observation data analysis, climate monitoring, and agricultural applications of satellite technology.',
    requirements: [
      'PhD in relevant field (Physics, Engineering, Earth Sciences)',
      'Research experience in space technology',
      'Programming skills (Python, R, MATLAB)',
      'Publication record in peer-reviewed journals',
      'Strong analytical and communication skills'
    ],
    responsibilities: [
      'Conduct independent research projects',
      'Publish research findings in top-tier journals',
      'Collaborate with international research teams',
      'Supervise graduate students',
      'Apply for research grants and funding'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Nairobi',
      country: 'Kenya',
    },
    salary: {
      min: 60000,
      max: 80000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Research & Development',
    tags: ['research', 'space technology', 'earth observation', 'climate'],
    skillsRequired: ['Python', 'Research', 'Data Analysis', 'Academic Writing'],
    experienceLevel: 'senior',
    education: ['PhD in relevant field'],
    benefits: ['Research budget', 'Conference travel', 'Publication support', 'Sabbatical opportunities'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 15,
      shortlisted: 4,
      interviewed: 2,
      hired: 0,
    },
  },
  {
    title: 'Educational Program Coordinator',
    company: 'African Space Research Institute',
    employerId: null,
    description: 'Develop and manage educational programs in space technology for students and professionals across Africa. Create curriculum, coordinate with universities, and organize training workshops.',
    requirements: [
      'Master\'s degree in Education or STEM field',
      '3+ years experience in educational program management',
      'Knowledge of space technology and applications',
      'Excellent communication and organizational skills',
      'Experience with online learning platforms'
    ],
    responsibilities: [
      'Design and implement educational curricula',
      'Coordinate with partner universities and institutions',
      'Organize workshops and training programs',
      'Manage online learning platforms',
      'Evaluate program effectiveness and outcomes'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Nairobi',
      country: 'Kenya',
    },
    salary: {
      min: 45000,
      max: 65000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Education & Training',
    tags: ['education', 'program management', 'curriculum development', 'training'],
    skillsRequired: ['Program Management', 'Curriculum Development', 'Communication', 'LMS'],
    experienceLevel: 'mid',
    education: ['Master\'s degree in Education or STEM'],
    benefits: ['Professional development', 'Travel opportunities', 'Flexible schedule', 'Training budget'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 32,
      shortlisted: 8,
      interviewed: 4,
      hired: 1,
    },
  },

  // Stellar Dynamics Corporation Jobs
  {
    title: 'Small Satellite Design Engineer',
    company: 'Stellar Dynamics Corporation',
    employerId: null,
    description: 'Design and develop innovative small satellite platforms for commercial and government clients. Work on CubeSat and microsatellite projects from concept to deployment.',
    requirements: [
      'Bachelor\'s degree in Aerospace/Mechanical Engineering',
      '3+ years experience in satellite design',
      'CAD software proficiency (SolidWorks, CATIA)',
      'Knowledge of space environment and materials',
      'Experience with satellite testing and qualification'
    ],
    responsibilities: [
      'Design satellite structures and mechanisms',
      'Perform structural analysis and simulations',
      'Develop test procedures and protocols',
      'Support satellite integration and testing',
      'Collaborate with customers on requirements'
    ],
    type: 'full-time',
    location: {
      type: 'on-site',
      city: 'Accra',
      country: 'Ghana',
    },
    salary: {
      min: 70000,
      max: 95000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Satellite Engineering',
    tags: ['satellite design', 'cubesat', 'mechanical engineering', 'CAD'],
    skillsRequired: ['SolidWorks', 'CATIA', 'Structural Analysis', 'Satellite Design'],
    experienceLevel: 'mid',
    education: ['Bachelor\'s degree in Aerospace/Mechanical Engineering'],
    benefits: ['Stock options', 'Health insurance', 'Innovation time', 'Startup environment'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 41,
      shortlisted: 9,
      interviewed: 5,
      hired: 0,
    },
  },
  {
    title: 'Launch Operations Specialist',
    company: 'Stellar Dynamics Corporation',
    employerId: null,
    description: 'Manage launch operations for small satellite missions. Coordinate with launch providers, oversee mission planning, and ensure successful satellite deployment.',
    requirements: [
      'Bachelor\'s degree in Engineering or related field',
      '2+ years experience in aerospace operations',
      'Knowledge of launch vehicle interfaces',
      'Project management experience',
      'Strong problem-solving and communication skills'
    ],
    responsibilities: [
      'Coordinate with launch service providers',
      'Develop mission timelines and procedures',
      'Oversee satellite integration and testing',
      'Monitor launch operations and deployment',
      'Manage customer communications during missions'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Accra',
      country: 'Ghana',
    },
    salary: {
      min: 65000,
      max: 85000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Operations',
    tags: ['launch operations', 'project management', 'mission planning'],
    skillsRequired: ['Project Management', 'Operations', 'Communication', 'Problem Solving'],
    experienceLevel: 'mid',
    education: ['Bachelor\'s degree in Engineering'],
    benefits: ['Travel opportunities', 'Performance bonuses', 'Professional development', 'Flexible hours'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 25,
      shortlisted: 6,
      interviewed: 3,
      hired: 0,
    },
  },
  {
    title: 'Business Development Manager - Space Services',
    company: 'Stellar Dynamics Corporation',
    employerId: null,
    description: 'Drive business growth by identifying new market opportunities and developing strategic partnerships in the African space sector. Focus on commercial and government clients.',
    requirements: [
      'MBA or Bachelor\'s in Business/Engineering',
      '5+ years experience in business development',
      'Knowledge of space industry and markets',
      'Strong networking and relationship building skills',
      'Experience with government and commercial sales'
    ],
    responsibilities: [
      'Identify and pursue new business opportunities',
      'Develop strategic partnerships and alliances',
      'Manage customer relationships and contracts',
      'Represent company at industry events',
      'Develop market analysis and business cases'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Accra',
      country: 'Ghana',
    },
    salary: {
      min: 80000,
      max: 120000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Business Development',
    tags: ['business development', 'sales', 'partnerships', 'space industry'],
    skillsRequired: ['Business Development', 'Sales', 'Networking', 'Market Analysis'],
    experienceLevel: 'senior',
    education: ['MBA or Bachelor\'s in Business/Engineering'],
    benefits: ['Commission structure', 'Travel budget', 'Networking events', 'Stock options'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 18,
      shortlisted: 5,
      interviewed: 2,
      hired: 0,
    },
  },

  // Continental Space Alliance Jobs
  {
    title: 'Space Policy Analyst',
    company: 'Continental Space Alliance',
    employerId: null,
    description: 'Analyze space policies and regulations across African countries. Develop policy recommendations and support the harmonization of space governance frameworks across the continent.',
    requirements: [
      'Master\'s degree in International Relations, Law, or Policy Studies',
      '3+ years experience in policy analysis',
      'Knowledge of space law and international treaties',
      'Strong research and analytical skills',
      'Excellent written and verbal communication'
    ],
    responsibilities: [
      'Analyze national and international space policies',
      'Develop policy recommendations and frameworks',
      'Support government stakeholders in policy development',
      'Conduct research on space governance issues',
      'Prepare reports and briefing materials'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Johannesburg',
      country: 'South Africa',
    },
    salary: {
      min: 55000,
      max: 75000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Policy & Governance',
    tags: ['policy analysis', 'space law', 'governance', 'research'],
    skillsRequired: ['Policy Analysis', 'Research', 'Space Law', 'Communication'],
    experienceLevel: 'mid',
    education: ['Master\'s degree in relevant field'],
    benefits: ['International travel', 'Conference attendance', 'Professional development', 'Diplomatic exposure'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 22,
      shortlisted: 7,
      interviewed: 3,
      hired: 0,
    },
  },
  {
    title: 'International Cooperation Coordinator',
    company: 'Continental Space Alliance',
    employerId: null,
    description: 'Facilitate international cooperation and partnerships in space activities. Coordinate with space agencies, international organizations, and diplomatic missions.',
    requirements: [
      'Master\'s degree in International Relations or related field',
      '4+ years experience in international cooperation',
      'Knowledge of space sector and international organizations',
      'Multilingual capabilities (English, French, Arabic preferred)',
      'Strong diplomatic and negotiation skills'
    ],
    responsibilities: [
      'Coordinate international space cooperation initiatives',
      'Facilitate partnerships between African and international space agencies',
      'Organize diplomatic meetings and conferences',
      'Develop cooperation agreements and MOUs',
      'Represent the Alliance at international forums'
    ],
    type: 'full-time',
    location: {
      type: 'hybrid',
      city: 'Johannesburg',
      country: 'South Africa',
    },
    salary: {
      min: 70000,
      max: 95000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'International Relations',
    tags: ['international cooperation', 'diplomacy', 'partnerships', 'space agencies'],
    skillsRequired: ['Diplomacy', 'Negotiation', 'International Relations', 'Multilingual'],
    experienceLevel: 'senior',
    education: ['Master\'s degree in International Relations'],
    benefits: ['Diplomatic status', 'International travel', 'Language training', 'Networking opportunities'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 16,
      shortlisted: 4,
      interviewed: 2,
      hired: 0,
    },
  },
];

// Organization Users (with 'organisation' role)
export const sampleOrganizations = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'African Space Research Institute',
    phone: '+254-700-123456',
    role: 'organisation',
    profile: {
      bio: 'Leading research institute advancing space technology across Africa through education, research, and innovation. Established in 2015, we focus on satellite technology, space science education, and capacity building for the African space sector.',
      skills: ['Research', 'Education', 'Satellite Technology', 'Space Science', 'Capacity Building'],
      interests: ['Space Technology', 'Education', 'Research', 'Innovation', 'African Development'],
      education: 'Research Institute',
      experience: '8+ years in space research and education',
      location: 'Nairobi, Kenya',
      currentLevel: 'advanced',
      careerGoals: ['Advance African space capabilities', 'Train next generation of space professionals', 'Foster space innovation'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: false,
      },
      language: 'en',
      timezone: 'Africa/Nairobi',
    },
    employment: {
      isEmployer: true,
      companyVerified: true,
      company: 'African Space Research Institute',
      position: 'Organization',
      jobsPosted: 5,
      companyInfo: {
        name: 'African Space Research Institute',
        website: 'https://asri.africa',
        size: '50-100 employees',
        industry: 'Space Technology & Research',
      },
    },
    partnership: {
      isPartner: true,
      isApproved: true,
      organizationInfo: {
        name: 'African Space Research Institute',
        type: 'Research Institute',
        region: 'East Africa',
        website: 'https://asri.africa',
      },
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Stellar Dynamics Corporation',
    phone: '+233-50-789012',
    role: 'organisation',
    profile: {
      bio: 'Private space technology company specializing in small satellite development, launch services, and space-based solutions for commercial and government clients. Founded in 2018, we are pioneering affordable access to space for African organizations.',
      skills: ['Satellite Manufacturing', 'Launch Services', 'Space Engineering', 'Project Management', 'Business Development'],
      interests: ['Commercial Space', 'Small Satellites', 'Launch Technology', 'Space Economy', 'Innovation'],
      education: 'Private Corporation',
      experience: '5+ years in commercial space sector',
      location: 'Accra, Ghana',
      currentLevel: 'advanced',
      careerGoals: ['Expand African space industry', 'Provide affordable space access', 'Drive space commercialization'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Accra',
    },
    employment: {
      isEmployer: true,
      companyVerified: true,
      company: 'Stellar Dynamics Corporation',
      position: 'Organization',
      jobsPosted: 8,
      companyInfo: {
        name: 'Stellar Dynamics Corporation',
        website: 'https://stellardynamics.com',
        size: '20-50 employees',
        industry: 'Commercial Space Technology',
      },
    },
    partnership: {
      isPartner: true,
      isApproved: true,
      organizationInfo: {
        name: 'Stellar Dynamics Corporation',
        type: 'Private Company',
        region: 'West Africa',
        website: 'https://stellardynamics.com',
      },
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Continental Space Alliance',
    phone: '+27-11-456789',
    role: 'organisation',
    profile: {
      bio: 'Pan-African organization promoting space cooperation, policy development, and capacity building across the continent. We work with governments, institutions, and private sector to advance Africa\'s space agenda through collaborative programs and initiatives.',
      skills: ['Policy Development', 'International Cooperation', 'Capacity Building', 'Program Management', 'Strategic Planning'],
      interests: ['Space Policy', 'International Relations', 'Capacity Building', 'African Unity', 'Sustainable Development'],
      education: 'International Organization',
      experience: '10+ years in space policy and cooperation',
      location: 'Johannesburg, South Africa',
      currentLevel: 'advanced',
      careerGoals: ['Unite African space efforts', 'Develop space policies', 'Foster continental cooperation'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: false,
      },
      language: 'en',
      timezone: 'Africa/Johannesburg',
    },
    employment: {
      isEmployer: true,
      companyVerified: true,
      company: 'Continental Space Alliance',
      position: 'Organization',
      jobsPosted: 4,
      companyInfo: {
        name: 'Continental Space Alliance',
        website: 'https://csa.africa',
        size: '100+ employees',
        industry: 'Space Policy & Cooperation',
      },
    },
    partnership: {
      isPartner: true,
      isApproved: true,
      organizationInfo: {
        name: 'Continental Space Alliance',
        type: 'International Organization',
        region: 'Pan-African',
        website: 'https://csa.africa',
      },
    },
  },
];

// Mentor Users
export const sampleMentors = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Dr. Amara Okafor',
    phone: '+254-**********',
    role: 'mentor',
    profile: {
      bio: 'Satellite systems engineer with 15 years of experience in designing and deploying Earth observation satellites. Currently leading the Satellite Engineering Department at African Space Research Institute. Expert in orbital mechanics, satellite subsystems, and mission planning.',
      skills: ['Satellite Engineering', 'Orbital Mechanics', 'Systems Design', 'Mission Planning', 'Earth Observation', 'MATLAB/Simulink'],
      interests: ['Space Technology', 'Education', 'Mentorship', 'African Space Development', 'Women in STEM'],
      education: 'PhD in Aerospace Engineering from University of Cape Town, MSc in Satellite Technology from Surrey Space Centre',
      experience: '15+ years in satellite systems engineering, 5+ years in mentorship',
      location: 'Nairobi, Kenya',
      currentLevel: 'advanced',
      careerGoals: ['Advance satellite technology in Africa', 'Mentor next generation engineers', 'Promote women in space'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Nairobi',
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Prof. Kwame Asante',
    phone: '+233-24-567890',
    role: 'mentor',
    profile: {
      bio: 'AI/ML researcher and professor specializing in space applications of artificial intelligence. Lead AI scientist at Stellar Dynamics Corporation with expertise in satellite data analysis, autonomous space systems, and machine learning for space missions.',
      skills: ['Artificial Intelligence', 'Machine Learning', 'Python', 'TensorFlow', 'Satellite Data Analysis', 'Computer Vision', 'Deep Learning'],
      interests: ['AI in Space', 'Machine Learning', 'Data Science', 'Research', 'Innovation', 'Technology Transfer'],
      education: 'PhD in Computer Science from MIT, MSc in AI from Stanford University',
      experience: '12+ years in AI research, 8+ years in space applications',
      location: 'Accra, Ghana',
      currentLevel: 'advanced',
      careerGoals: ['Advance AI applications in space', 'Bridge academia and industry', 'Develop African AI talent'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Accra',
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Dr. Fatima Al-Rashid',
    phone: '+27-82-123456',
    role: 'mentor',
    profile: {
      bio: 'Cybersecurity expert and space law specialist with extensive experience in protecting space infrastructure and developing space governance frameworks. Senior advisor at Continental Space Alliance focusing on space security and international space law.',
      skills: ['Cybersecurity', 'Space Law', 'Information Security', 'Risk Assessment', 'Policy Development', 'International Relations'],
      interests: ['Space Security', 'International Law', 'Cybersecurity', 'Policy Development', 'Space Governance', 'Ethics in Space'],
      education: 'PhD in International Law from Oxford University, MSc in Cybersecurity from Imperial College London',
      experience: '10+ years in cybersecurity, 8+ years in space law and policy',
      location: 'Johannesburg, South Africa',
      currentLevel: 'advanced',
      careerGoals: ['Secure space infrastructure', 'Develop space governance', 'Promote responsible space activities'],
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: false,
      },
      language: 'en',
      timezone: 'Africa/Johannesburg',
    },
  },
];

export const sampleUsers = [...sampleOrganizations, ...sampleMentors];

// Events for each organization
export const sampleEvents = [
  // African Space Research Institute Events
  {
    title: 'African Satellite Technology Summit 2024',
    description: 'Join leading experts from across Africa for a comprehensive summit on satellite technology advancements, applications, and future opportunities. This three-day event will feature keynote presentations, technical workshops, and networking sessions focused on advancing Africa\'s space capabilities.',
    type: 'conference',
    category: 'Space Technology',
    organizer: {
      name: 'African Space Research Institute',
      organization: 'African Space Research Institute',
      email: '<EMAIL>',
      userId: null, // Will be set when we create users
    },
    speakers: [
      {
        name: 'Dr. Amara Okafor',
        title: 'Head of Satellite Engineering',
        organization: 'African Space Research Institute',
        bio: 'Leading satellite systems engineer with 15+ years of experience',
        avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',
      },
      {
        name: 'Prof. Sarah Mwangi',
        title: 'Director of Space Sciences',
        organization: 'University of Nairobi',
        bio: 'Renowned astrophysicist and space science educator',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      },
    ],
    startDate: new Date('2024-03-15T09:00:00Z'),
    endDate: new Date('2024-03-17T17:00:00Z'),
    timezone: 'Africa/Nairobi',
    location: {
      type: 'hybrid',
      venue: 'Kenyatta International Conference Centre',
      address: 'Harambee Avenue, Nairobi, Kenya',
      city: 'Nairobi',
      country: 'Kenya',
      onlineLink: 'https://zoom.us/j/asri-summit-2024',
    },
    capacity: 500,
    pricing: {
      isFree: false,
      earlyBird: 150,
      regular: 200,
      student: 50,
      currency: 'USD',
    },
    registration: {
      isOpen: true,
      deadline: new Date('2024-03-10T23:59:59Z'),
      requiresApproval: false,
    },
    tags: ['satellite technology', 'conference', 'networking', 'africa'],
    status: 'published',
    visibility: 'public',
    featured: true,
    analytics: {
      views: 1250,
      registrations: 287,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: true,
    recurringPattern: {
      frequency: 'yearly',
      interval: 1,
    },
  },
  {
    title: 'Earth Observation Data Analysis Workshop',
    description: 'Hands-on workshop focusing on processing and analyzing satellite Earth observation data for environmental monitoring, agriculture, and urban planning applications. Participants will learn to use Python, QGIS, and Google Earth Engine.',
    type: 'workshop',
    category: 'Data Science',
    organizer: {
      name: 'African Space Research Institute',
      organization: 'African Space Research Institute',
      email: '<EMAIL>',
      userId: null,
    },
    speakers: [
      {
        name: 'Dr. James Kimani',
        title: 'Remote Sensing Specialist',
        organization: 'African Space Research Institute',
        bio: 'Expert in satellite data analysis and environmental monitoring',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      },
    ],
    startDate: new Date('2024-02-20T08:00:00Z'),
    endDate: new Date('2024-02-22T16:00:00Z'),
    timezone: 'Africa/Nairobi',
    location: {
      type: 'on-site',
      venue: 'ASRI Training Center',
      address: 'University Way, Nairobi, Kenya',
      city: 'Nairobi',
      country: 'Kenya',
    },
    capacity: 30,
    pricing: {
      isFree: false,
      regular: 300,
      student: 150,
      currency: 'USD',
    },
    registration: {
      isOpen: true,
      deadline: new Date('2024-02-15T23:59:59Z'),
      requiresApproval: true,
    },
    tags: ['earth observation', 'data analysis', 'python', 'workshop'],
    status: 'published',
    visibility: 'public',
    featured: false,
    analytics: {
      views: 450,
      registrations: 28,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: false,
  },
  {
    title: 'Women in Space Technology Networking Event',
    description: 'Networking event celebrating and supporting women in the space technology sector across Africa. Join us for inspiring talks, mentorship opportunities, and building connections within the African space community.',
    type: 'networking',
    category: 'Professional Development',
    organizer: {
      name: 'African Space Research Institute',
      organization: 'African Space Research Institute',
      email: '<EMAIL>',
      userId: null,
    },
    speakers: [
      {
        name: 'Dr. Amara Okafor',
        title: 'Head of Satellite Engineering',
        organization: 'African Space Research Institute',
        bio: 'Advocate for women in STEM and space technology',
        avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',
      },
      {
        name: 'Eng. Grace Wanjiku',
        title: 'Spacecraft Engineer',
        organization: 'Kenya Space Agency',
        bio: 'Young professional making waves in spacecraft design',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      },
    ],
    startDate: new Date('2024-03-08T18:00:00Z'),
    endDate: new Date('2024-03-08T21:00:00Z'),
    timezone: 'Africa/Nairobi',
    location: {
      type: 'on-site',
      venue: 'Radisson Blu Hotel',
      address: 'Upper Hill, Nairobi, Kenya',
      city: 'Nairobi',
      country: 'Kenya',
    },
    capacity: 100,
    pricing: {
      isFree: true,
      currency: 'USD',
    },
    registration: {
      isOpen: true,
      deadline: new Date('2024-03-06T23:59:59Z'),
      requiresApproval: false,
    },
    tags: ['women in tech', 'networking', 'mentorship', 'diversity'],
    status: 'published',
    visibility: 'public',
    featured: true,
    analytics: {
      views: 680,
      registrations: 85,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: true,
    recurringPattern: {
      frequency: 'yearly',
      interval: 1,
    },
  },

  // Stellar Dynamics Corporation Events
  {
    title: 'CubeSat Development Bootcamp',
    description: 'Intensive 5-day bootcamp covering all aspects of CubeSat development from design to deployment. Participants will work in teams to design, build, and test a functional CubeSat prototype using industry-standard tools and methodologies.',
    type: 'workshop',
    category: 'Satellite Engineering',
    organizer: {
      name: 'Stellar Dynamics Corporation',
      organization: 'Stellar Dynamics Corporation',
      email: '<EMAIL>',
      userId: null,
    },
    speakers: [
      {
        name: 'Prof. Kwame Asante',
        title: 'Lead AI Scientist',
        organization: 'Stellar Dynamics Corporation',
        bio: 'Expert in AI applications for space missions and CubeSat technology',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      },
      {
        name: 'Eng. Michael Osei',
        title: 'CubeSat Systems Engineer',
        organization: 'Stellar Dynamics Corporation',
        bio: 'Specialist in small satellite design and integration',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      },
    ],
    startDate: new Date('2024-04-08T09:00:00Z'),
    endDate: new Date('2024-04-12T17:00:00Z'),
    timezone: 'Africa/Accra',
    location: {
      type: 'on-site',
      venue: 'Stellar Dynamics Facility',
      address: 'Airport Residential Area, Accra, Ghana',
      city: 'Accra',
      country: 'Ghana',
    },
    capacity: 20,
    pricing: {
      isFree: false,
      regular: 800,
      student: 400,
      currency: 'USD',
    },
    registration: {
      isOpen: true,
      deadline: new Date('2024-04-01T23:59:59Z'),
      requiresApproval: true,
    },
    tags: ['cubesat', 'satellite design', 'hands-on', 'bootcamp'],
    status: 'published',
    visibility: 'public',
    featured: true,
    analytics: {
      views: 320,
      registrations: 18,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: false,
  },
  {
    title: 'Commercial Space Industry Forum',
    description: 'Forum bringing together entrepreneurs, investors, and industry leaders to discuss opportunities and challenges in the African commercial space sector. Focus on business models, funding, and market development.',
    type: 'conference',
    category: 'Business & Entrepreneurship',
    organizer: {
      name: 'Stellar Dynamics Corporation',
      organization: 'Stellar Dynamics Corporation',
      email: '<EMAIL>',
      userId: null,
    },
    speakers: [
      {
        name: 'CEO John Mensah',
        title: 'Chief Executive Officer',
        organization: 'Stellar Dynamics Corporation',
        bio: 'Entrepreneur and leader in African commercial space sector',
        avatar: 'https://images.unsplash.com/photo-**********-0b93528c311a?w=150',
      },
      {
        name: 'Dr. Fatou Diallo',
        title: 'Space Investment Analyst',
        organization: 'African Development Bank',
        bio: 'Expert in space sector financing and investment',
        avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',
      },
    ],
    startDate: new Date('2024-05-22T09:00:00Z'),
    endDate: new Date('2024-05-23T17:00:00Z'),
    timezone: 'Africa/Accra',
    location: {
      type: 'hybrid',
      venue: 'Accra International Conference Centre',
      address: 'Liberation Road, Accra, Ghana',
      city: 'Accra',
      country: 'Ghana',
      onlineLink: 'https://teams.microsoft.com/stellar-forum-2024',
    },
    capacity: 200,
    pricing: {
      isFree: false,
      earlyBird: 100,
      regular: 150,
      startup: 75,
      currency: 'USD',
    },
    registration: {
      isOpen: true,
      deadline: new Date('2024-05-20T23:59:59Z'),
      requiresApproval: false,
    },
    tags: ['commercial space', 'entrepreneurship', 'investment', 'business'],
    status: 'published',
    visibility: 'public',
    featured: true,
    analytics: {
      views: 890,
      registrations: 145,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: true,
    recurringPattern: {
      frequency: 'yearly',
      interval: 1,
    },
  },

  // Continental Space Alliance Events
  {
    title: 'African Space Policy Symposium',
    description: 'High-level symposium bringing together government officials, policy makers, and space industry leaders to discuss space governance, regulations, and policy harmonization across African countries.',
    type: 'conference',
    category: 'Space Policy',
    organizer: {
      name: 'Continental Space Alliance',
      organization: 'Continental Space Alliance',
      email: '<EMAIL>',
      userId: null,
    },
    speakers: [
      {
        name: 'Dr. Fatima Al-Rashid',
        title: 'Senior Space Policy Advisor',
        organization: 'Continental Space Alliance',
        bio: 'Expert in international space law and policy development',
        avatar: 'https://images.unsplash.com/photo-*************-2616b612b786?w=150',
      },
      {
        name: 'Hon. Minister David Kone',
        title: 'Minister of Science and Technology',
        organization: 'Government of Ivory Coast',
        bio: 'Government leader in science and technology policy',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      },
    ],
    startDate: new Date('2024-06-10T09:00:00Z'),
    endDate: new Date('2024-06-12T17:00:00Z'),
    timezone: 'Africa/Johannesburg',
    location: {
      type: 'on-site',
      venue: 'Sandton Convention Centre',
      address: 'Maude Street, Sandton, Johannesburg, South Africa',
      city: 'Johannesburg',
      country: 'South Africa',
    },
    capacity: 300,
    pricing: {
      isFree: true,
      currency: 'USD',
    },
    registration: {
      isOpen: true,
      deadline: new Date('2024-06-05T23:59:59Z'),
      requiresApproval: true,
    },
    tags: ['space policy', 'governance', 'government', 'regulation'],
    status: 'published',
    visibility: 'members_only',
    featured: true,
    analytics: {
      views: 420,
      registrations: 180,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: true,
    recurringPattern: {
      frequency: 'yearly',
      interval: 1,
    },
  },
  {
    title: 'International Space Cooperation Workshop',
    description: 'Workshop focused on building partnerships between African space organizations and international space agencies. Discussions on joint missions, technology transfer, and capacity building initiatives.',
    type: 'workshop',
    category: 'International Cooperation',
    organizer: {
      name: 'Continental Space Alliance',
      organization: 'Continental Space Alliance',
      email: '<EMAIL>',
      userId: null,
    },
    speakers: [
      {
        name: 'Dr. Maria Santos',
        title: 'International Relations Director',
        organization: 'European Space Agency',
        bio: 'Expert in international space cooperation and partnerships',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      },
      {
        name: 'Ambassador Peter Ochieng',
        title: 'Space Diplomacy Advisor',
        organization: 'Continental Space Alliance',
        bio: 'Specialist in space diplomacy and international relations',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      },
    ],
    startDate: new Date('2024-07-15T09:00:00Z'),
    endDate: new Date('2024-07-17T17:00:00Z'),
    timezone: 'Africa/Johannesburg',
    location: {
      type: 'hybrid',
      venue: 'University of the Witwatersrand',
      address: 'Jan Smuts Avenue, Braamfontein, Johannesburg, South Africa',
      city: 'Johannesburg',
      country: 'South Africa',
      onlineLink: 'https://webex.com/csa-cooperation-2024',
    },
    capacity: 80,
    pricing: {
      isFree: true,
      currency: 'USD',
    },
    registration: {
      isOpen: true,
      deadline: new Date('2024-07-10T23:59:59Z'),
      requiresApproval: true,
    },
    tags: ['international cooperation', 'partnerships', 'space agencies', 'diplomacy'],
    status: 'published',
    visibility: 'public',
    featured: false,
    analytics: {
      views: 280,
      registrations: 65,
      attendanceRate: 0,
      averageRating: 0,
      totalRatings: 0,
    },
    isRecurring: false,
  },
];

// Educational Resources
export const sampleResources = [
  // African Space Research Institute Resources
  {
    title: 'Satellite Systems Engineering Handbook',
    description: 'Comprehensive handbook covering all aspects of satellite systems engineering, from mission design to on-orbit operations. Essential reading for space engineering professionals and students.',
    type: 'pdf',
    category: 'Space Technology',
    subcategory: 'Satellite Engineering',
    tags: ['satellite', 'engineering', 'handbook', 'reference'],
    author: {
      name: 'Dr. Amara Okafor',
      organization: 'African Space Research Institute',
      email: '<EMAIL>',
      userId: null,
    },
    file: {
      originalName: 'satellite-systems-handbook.pdf',
      fileName: 'satellite-systems-handbook-2024.pdf',
      url: 'https://resources.asri.africa/handbooks/satellite-systems-handbook-2024.pdf',
      size: 15728640, // 15MB
      mimeType: 'application/pdf',
      uploadedAt: new Date('2024-01-15T10:00:00Z'),
    },
    metadata: {
      pages: 450,
      language: 'en',
      level: 'intermediate',
      subject: 'Satellite Engineering',
    },
    access: {
      isPublic: true,
      requiresRegistration: false,
      allowedRoles: ['all'],
    },
    analytics: {
      downloads: 2340,
      views: 5680,
      averageRating: 4.7,
      totalRatings: 156,
    },
    isActive: true,
    isFeatured: true,
  },
  {
    title: 'Earth Observation Data Processing Tutorial Series',
    description: 'Step-by-step video tutorial series on processing satellite Earth observation data using Python, QGIS, and Google Earth Engine. Perfect for beginners and intermediate users.',
    type: 'video',
    category: 'Data Science',
    subcategory: 'Earth Observation',
    tags: ['earth observation', 'python', 'qgis', 'tutorial', 'data processing'],
    author: {
      name: 'Dr. James Kimani',
      organization: 'African Space Research Institute',
      email: '<EMAIL>',
      userId: null,
    },
    file: {
      originalName: 'eo-data-processing-series.mp4',
      fileName: 'eo-data-processing-complete-series.mp4',
      url: 'https://resources.asri.africa/videos/eo-data-processing-complete-series.mp4',
      size: 2147483648, // 2GB
      mimeType: 'video/mp4',
      uploadedAt: new Date('2024-01-20T14:30:00Z'),
    },
    metadata: {
      duration: 18000, // 5 hours
      resolution: '1920x1080',
      language: 'en',
      level: 'beginner',
      subject: 'Earth Observation',
    },
    access: {
      isPublic: true,
      requiresRegistration: true,
      allowedRoles: ['student', 'mentor', 'organisation'],
    },
    analytics: {
      downloads: 890,
      views: 3420,
      averageRating: 4.9,
      totalRatings: 89,
    },
    isActive: true,
    isFeatured: true,
  },

  // Stellar Dynamics Corporation Resources
  {
    title: 'CubeSat Design and Development Guide',
    description: 'Practical guide for designing and developing CubeSats, covering mechanical design, electronics integration, testing procedures, and launch preparation. Based on real industry experience.',
    type: 'pdf',
    category: 'Satellite Engineering',
    subcategory: 'CubeSat',
    tags: ['cubesat', 'design', 'development', 'guide', 'small satellites'],
    author: {
      name: 'Eng. Michael Osei',
      organization: 'Stellar Dynamics Corporation',
      email: '<EMAIL>',
      userId: null,
    },
    file: {
      originalName: 'cubesat-design-guide.pdf',
      fileName: 'cubesat-design-development-guide-v2.pdf',
      url: 'https://resources.stellardynamics.com/guides/cubesat-design-development-guide-v2.pdf',
      size: 8388608, // 8MB
      mimeType: 'application/pdf',
      uploadedAt: new Date('2024-02-01T09:15:00Z'),
    },
    metadata: {
      pages: 180,
      language: 'en',
      level: 'intermediate',
      subject: 'CubeSat Engineering',
    },
    access: {
      isPublic: false,
      requiresRegistration: true,
      allowedRoles: ['mentor', 'organisation'],
    },
    analytics: {
      downloads: 567,
      views: 1234,
      averageRating: 4.6,
      totalRatings: 45,
    },
    isActive: true,
    isFeatured: false,
  },
  {
    title: 'Commercial Space Business Models Presentation',
    description: 'Comprehensive presentation covering various business models in the commercial space industry, market analysis, and strategies for African space startups.',
    type: 'presentation',
    category: 'Business & Entrepreneurship',
    subcategory: 'Space Business',
    tags: ['business models', 'commercial space', 'entrepreneurship', 'market analysis'],
    author: {
      name: 'CEO John Mensah',
      organization: 'Stellar Dynamics Corporation',
      email: '<EMAIL>',
      userId: null,
    },
    file: {
      originalName: 'space-business-models.pptx',
      fileName: 'commercial-space-business-models-2024.pptx',
      url: 'https://resources.stellardynamics.com/presentations/commercial-space-business-models-2024.pptx',
      size: 52428800, // 50MB
      mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      uploadedAt: new Date('2024-02-10T16:45:00Z'),
    },
    metadata: {
      pages: 85,
      language: 'en',
      level: 'intermediate',
      subject: 'Space Business',
    },
    access: {
      isPublic: true,
      requiresRegistration: false,
      allowedRoles: ['all'],
    },
    analytics: {
      downloads: 1123,
      views: 2890,
      averageRating: 4.4,
      totalRatings: 67,
    },
    isActive: true,
    isFeatured: true,
  },

  // Continental Space Alliance Resources
  {
    title: 'African Space Policy Framework Document',
    description: 'Comprehensive policy framework document outlining recommendations for space governance, regulations, and cooperation mechanisms across African countries.',
    type: 'document',
    category: 'Space Policy',
    subcategory: 'Governance',
    tags: ['space policy', 'governance', 'africa', 'framework', 'regulation'],
    author: {
      name: 'Dr. Fatima Al-Rashid',
      organization: 'Continental Space Alliance',
      email: '<EMAIL>',
      userId: null,
    },
    file: {
      originalName: 'african-space-policy-framework.docx',
      fileName: 'african-space-policy-framework-2024.docx',
      url: 'https://resources.csa.africa/policies/african-space-policy-framework-2024.docx',
      size: 4194304, // 4MB
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      uploadedAt: new Date('2024-01-25T11:20:00Z'),
    },
    metadata: {
      pages: 120,
      language: 'en',
      level: 'advanced',
      subject: 'Space Policy',
    },
    access: {
      isPublic: false,
      requiresRegistration: true,
      allowedRoles: ['organisation', 'mentor'],
    },
    analytics: {
      downloads: 234,
      views: 567,
      averageRating: 4.8,
      totalRatings: 23,
    },
    isActive: true,
    isFeatured: false,
  },
  {
    title: 'International Space Law Reference Database',
    description: 'Comprehensive database of international space law documents, treaties, and agreements relevant to African space activities and international cooperation.',
    type: 'dataset',
    category: 'Space Law',
    subcategory: 'International Law',
    tags: ['space law', 'international law', 'treaties', 'database', 'reference'],
    author: {
      name: 'Legal Research Team',
      organization: 'Continental Space Alliance',
      email: '<EMAIL>',
      userId: null,
    },
    file: {
      originalName: 'space-law-database.zip',
      fileName: 'international-space-law-database-2024.zip',
      url: 'https://resources.csa.africa/databases/international-space-law-database-2024.zip',
      size: 104857600, // 100MB
      mimeType: 'application/zip',
      uploadedAt: new Date('2024-02-05T13:10:00Z'),
    },
    metadata: {
      language: 'en',
      level: 'advanced',
      subject: 'Space Law',
    },
    access: {
      isPublic: true,
      requiresRegistration: true,
      allowedRoles: ['mentor', 'organisation'],
    },
    analytics: {
      downloads: 145,
      views: 389,
      averageRating: 4.5,
      totalRatings: 18,
    },
    isActive: true,
    isFeatured: false,
  },
];

// Mentor Profiles
export const sampleMentorProfiles = [
  {
    userId: null, // Will be set to Dr. Amara Okafor's ID
    isApproved: true,
    expertise: [
      'Satellite Systems Engineering',
      'Orbital Mechanics',
      'Mission Design',
      'Earth Observation',
      'Satellite Subsystems',
      'ADCS (Attitude Determination and Control)',
      'Power Systems',
      'Communications Systems'
    ],
    specialization: 'Satellite Systems Engineering',
    experience: 15,
    education: [
      'PhD in Aerospace Engineering - University of Cape Town',
      'MSc in Satellite Technology - Surrey Space Centre, UK',
      'BSc in Electrical Engineering - University of Nairobi'
    ],
    certifications: [
      'Project Management Professional (PMP)',
      'Systems Engineering Professional (CSEP)',
      'Satellite Communications Specialist'
    ],
    languages: ['English', 'Swahili', 'French'],
    timezone: 'Africa/Nairobi',
    availability: {
      days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      timeSlots: [
        { start: '09:00', end: '12:00' },
        { start: '14:00', end: '17:00' }
      ],
    },
    pricing: {
      hourlyRate: 150,
      currency: 'USD',
      offersFreeSession: true,
    },
    bio: 'Dr. Amara Okafor is a leading satellite systems engineer with over 15 years of experience in designing and deploying Earth observation satellites. She currently heads the Satellite Engineering Department at the African Space Research Institute and has been instrumental in several successful satellite missions across Africa. Dr. Okafor is passionate about mentoring the next generation of space professionals and promoting women in STEM fields.',
    achievements: [
      'Led design team for 5 successful satellite missions',
      'Published 40+ peer-reviewed papers in space technology',
      'Recipient of African Space Excellence Award 2023',
      'Mentored 50+ junior engineers and students',
      'Keynote speaker at 20+ international conferences'
    ],
    totalSessions: 127,
    averageRating: 4.9,
    totalRatings: 89,
    responseTime: 4, // hours
    isActive: true,
    mentorshipAreas: [
      'Career guidance in space engineering',
      'Satellite mission design and planning',
      'Technical skills development',
      'Research methodology and publication',
      'Leadership and team management',
      'Women in STEM advocacy'
    ],
    preferredCommunication: ['Video Call', 'Email', 'Phone'],
    sessionTypes: ['One-on-one mentoring', 'Group sessions', 'Technical reviews', 'Career counseling'],
  },
  {
    userId: null, // Will be set to Prof. Kwame Asante's ID
    isApproved: true,
    expertise: [
      'Artificial Intelligence',
      'Machine Learning',
      'Deep Learning',
      'Computer Vision',
      'Satellite Data Analysis',
      'Python Programming',
      'TensorFlow/PyTorch',
      'Space Mission AI Applications'
    ],
    specialization: 'AI Applications in Space Technology',
    experience: 12,
    education: [
      'PhD in Computer Science - Massachusetts Institute of Technology',
      'MSc in Artificial Intelligence - Stanford University',
      'BSc in Computer Engineering - University of Ghana'
    ],
    certifications: [
      'Google Cloud Professional ML Engineer',
      'AWS Certified Machine Learning Specialist',
      'NVIDIA Deep Learning Institute Instructor',
      'TensorFlow Developer Certificate'
    ],
    languages: ['English', 'Twi', 'French'],
    timezone: 'Africa/Accra',
    availability: {
      days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Saturday'],
      timeSlots: [
        { start: '08:00', end: '11:00' },
        { start: '15:00', end: '18:00' }
      ],
    },
    pricing: {
      hourlyRate: 120,
      currency: 'USD',
      offersFreeSession: true,
    },
    bio: 'Prof. Kwame Asante is a renowned AI researcher and professor specializing in space applications of artificial intelligence. He serves as the Lead AI Scientist at Stellar Dynamics Corporation while maintaining his academic position. Prof. Asante has pioneered several breakthrough AI algorithms for satellite data analysis and autonomous space systems, bridging the gap between cutting-edge research and practical industry applications.',
    achievements: [
      'Developed AI algorithms used in 10+ satellite missions',
      'Published 60+ research papers in top-tier AI and space journals',
      'Holds 8 patents in AI for space applications',
      'Founded AI4Space research lab',
      'Trained 200+ students and professionals in AI/ML'
    ],
    totalSessions: 203,
    averageRating: 4.8,
    totalRatings: 134,
    responseTime: 6, // hours
    isActive: true,
    mentorshipAreas: [
      'AI/ML career development',
      'Research project guidance',
      'Technical skill building in AI/ML',
      'Academic-industry transition',
      'Startup and entrepreneurship advice',
      'Grant writing and funding applications'
    ],
    preferredCommunication: ['Video Call', 'Email', 'Slack'],
    sessionTypes: ['Technical mentoring', 'Research guidance', 'Code reviews', 'Career planning'],
  },
  {
    userId: null, // Will be set to Dr. Fatima Al-Rashid's ID
    isApproved: true,
    expertise: [
      'Cybersecurity',
      'Space Law',
      'International Relations',
      'Policy Development',
      'Risk Assessment',
      'Information Security',
      'Space Governance',
      'Regulatory Compliance'
    ],
    specialization: 'Space Security and International Law',
    experience: 10,
    education: [
      'PhD in International Law - Oxford University',
      'MSc in Cybersecurity - Imperial College London',
      'LLB in International Law - University of Cape Town'
    ],
    certifications: [
      'Certified Information Security Manager (CISM)',
      'Certified Information Systems Security Professional (CISSP)',
      'International Space Law Certificate - Leiden University',
      'Diplomatic Protocol Certification'
    ],
    languages: ['English', 'Arabic', 'French', 'Afrikaans'],
    timezone: 'Africa/Johannesburg',
    availability: {
      days: ['Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      timeSlots: [
        { start: '10:00', end: '13:00' },
        { start: '15:00', end: '17:00' }
      ],
    },
    pricing: {
      hourlyRate: 180,
      currency: 'USD',
      offersFreeSession: false,
    },
    bio: 'Dr. Fatima Al-Rashid is a distinguished expert in cybersecurity and space law with extensive experience in protecting space infrastructure and developing international space governance frameworks. She serves as Senior Space Policy Advisor at the Continental Space Alliance, where she leads initiatives on space security and international cooperation. Dr. Al-Rashid is recognized globally for her contributions to space law and policy development.',
    achievements: [
      'Authored 3 books on space law and cybersecurity',
      'Advised 15+ governments on space policy development',
      'Led cybersecurity assessments for major space missions',
      'Recipient of International Space Law Award 2022',
      'Expert witness in international space law cases'
    ],
    totalSessions: 89,
    averageRating: 4.9,
    totalRatings: 67,
    responseTime: 8, // hours
    isActive: true,
    mentorshipAreas: [
      'Space law and policy career guidance',
      'Cybersecurity in space systems',
      'International relations and diplomacy',
      'Legal research and writing',
      'Policy development and analysis',
      'Professional networking in international organizations'
    ],
    preferredCommunication: ['Video Call', 'Email'],
    sessionTypes: ['Strategic consulting', 'Legal guidance', 'Policy review', 'Career mentoring'],
  },
];

export async function seedDatabase() {
  try {
    await connectDB();

    console.log('Seeding database...');

    // Clear existing data
    await User.deleteMany({});
    await Course.deleteMany({});
    await Job.deleteMany({});
    await Event.deleteMany({});
    await Resource.deleteMany({});
    await MentorProfile.deleteMany({});

    console.log('Creating users...');
    // Create users first
    const hashedPassword = await bcrypt.hash('password123', 12);
    const users = await User.insertMany(
      sampleUsers.map(user => ({
        ...user,
        password: hashedPassword,
      }))
    );

    console.log(`Created ${users.length} users`);

    // Map users by email for easy reference
    const userMap = new Map();
    users.forEach(user => {
      userMap.set(user.email, user);
    });

    // Get organization and mentor users
    const asriUser = userMap.get('<EMAIL>');
    const stellarUser = userMap.get('<EMAIL>');
    const csaUser = userMap.get('<EMAIL>');
    const amaraUser = userMap.get('<EMAIL>');
    const kwameUser = userMap.get('<EMAIL>');
    const fatimaUser = userMap.get('<EMAIL>');

    console.log('Creating courses...');
    // Create courses with instructor IDs
    const coursesWithInstructors = sampleCourses.map((course, index) => {
      let instructorId;
      if (course.instructor === 'Dr. Amara Okafor') {
        instructorId = amaraUser._id;
      } else if (course.instructor === 'Prof. Kwame Asante') {
        instructorId = kwameUser._id;
      } else if (course.instructor === 'Dr. Fatima Al-Rashid') {
        instructorId = fatimaUser._id;
      } else {
        instructorId = amaraUser._id; // Default to Amara
      }

      return {
        ...course,
        instructorId,
      };
    });

    await Course.insertMany(coursesWithInstructors);
    console.log(`Created ${coursesWithInstructors.length} courses`);

    console.log('Creating jobs...');
    // Create jobs with employer IDs
    const jobsWithEmployers = sampleJobs.map(job => {
      let employerId;
      if (job.company === 'African Space Research Institute') {
        employerId = asriUser._id;
      } else if (job.company === 'Stellar Dynamics Corporation') {
        employerId = stellarUser._id;
      } else if (job.company === 'Continental Space Alliance') {
        employerId = csaUser._id;
      } else {
        employerId = asriUser._id; // Default
      }

      return {
        ...job,
        employerId,
      };
    });

    await Job.insertMany(jobsWithEmployers);
    console.log(`Created ${jobsWithEmployers.length} jobs`);

    console.log('Creating events...');
    // Create events with organizer IDs
    const eventsWithOrganizers = sampleEvents.map(event => {
      let organizerUserId;
      if (event.organizer.organization === 'African Space Research Institute') {
        organizerUserId = asriUser._id;
      } else if (event.organizer.organization === 'Stellar Dynamics Corporation') {
        organizerUserId = stellarUser._id;
      } else if (event.organizer.organization === 'Continental Space Alliance') {
        organizerUserId = csaUser._id;
      } else {
        organizerUserId = asriUser._id; // Default
      }

      return {
        ...event,
        organizer: {
          ...event.organizer,
          userId: organizerUserId,
        },
      };
    });

    await Event.insertMany(eventsWithOrganizers);
    console.log(`Created ${eventsWithOrganizers.length} events`);

    console.log('Creating resources...');
    // Create resources with author IDs
    const resourcesWithAuthors = sampleResources.map(resource => {
      let authorUserId;
      if (resource.author.organization === 'African Space Research Institute') {
        if (resource.author.name === 'Dr. Amara Okafor') {
          authorUserId = amaraUser._id;
        } else {
          authorUserId = asriUser._id;
        }
      } else if (resource.author.organization === 'Stellar Dynamics Corporation') {
        if (resource.author.name === 'Prof. Kwame Asante') {
          authorUserId = kwameUser._id;
        } else {
          authorUserId = stellarUser._id;
        }
      } else if (resource.author.organization === 'Continental Space Alliance') {
        if (resource.author.name === 'Dr. Fatima Al-Rashid') {
          authorUserId = fatimaUser._id;
        } else {
          authorUserId = csaUser._id;
        }
      } else {
        authorUserId = asriUser._id; // Default
      }

      return {
        ...resource,
        author: {
          ...resource.author,
          userId: authorUserId,
        },
      };
    });

    await Resource.insertMany(resourcesWithAuthors);
    console.log(`Created ${resourcesWithAuthors.length} resources`);

    console.log('Creating mentor profiles...');
    // Create mentor profiles
    const mentorProfilesWithUserIds = sampleMentorProfiles.map((profile, index) => {
      let userId;
      if (index === 0) {
        userId = amaraUser._id; // Dr. Amara Okafor
      } else if (index === 1) {
        userId = kwameUser._id; // Prof. Kwame Asante
      } else if (index === 2) {
        userId = fatimaUser._id; // Dr. Fatima Al-Rashid
      }

      return {
        ...profile,
        userId,
      };
    });

    await MentorProfile.insertMany(mentorProfilesWithUserIds);
    console.log(`Created ${mentorProfilesWithUserIds.length} mentor profiles`);

    console.log('Database seeded successfully!');
    console.log('\n=== SAMPLE LOGIN CREDENTIALS ===');
    console.log('\n--- ORGANIZATIONS ---');
    console.log('African Space Research Institute: <EMAIL> / password123');
    console.log('Stellar Dynamics Corporation: <EMAIL> / password123');
    console.log('Continental Space Alliance: <EMAIL> / password123');
    console.log('\n--- MENTORS ---');
    console.log('Dr. Amara Okafor (Satellite Engineering): <EMAIL> / password123');
    console.log('Prof. Kwame Asante (AI/ML): <EMAIL> / password123');
    console.log('Dr. Fatima Al-Rashid (Cybersecurity/Law): <EMAIL> / password123');
    console.log('\n=== SUMMARY ===');
    console.log(`✓ ${users.length} users created (3 organizations + 3 mentors)`);
    console.log(`✓ ${coursesWithInstructors.length} courses created`);
    console.log(`✓ ${jobsWithEmployers.length} job postings created`);
    console.log(`✓ ${eventsWithOrganizers.length} events created`);
    console.log(`✓ ${resourcesWithAuthors.length} educational resources created`);
    console.log(`✓ ${mentorProfilesWithUserIds.length} mentor profiles created`);

  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}
