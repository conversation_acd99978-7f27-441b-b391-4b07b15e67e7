import connectDB from './mongodb';
import Course from '@/models/Course';
import Job from '@/models/Job';
import User from '@/models/User';
import bcrypt from 'bcryptjs';

export const sampleCourses = [
  {
    title: 'Introduction to Satellite Engineering',
    description: 'Learn the fundamentals of satellite design, orbital mechanics, and space systems engineering. This comprehensive course covers everything from basic concepts to advanced satellite subsystems.',
    instructor: 'Dr. <PERSON>',
    instructorId: null, // Will be set when we create users
    category: 'Space Technology',
    level: 'beginner',
    duration: 40,
    thumbnail: 'https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?w=400',
    tags: ['satellite', 'engineering', 'space', 'orbital mechanics'],
    modules: [
      {
        title: 'Introduction to Space Systems',
        description: 'Overview of space systems and satellite fundamentals',
        order: 1,
        lessons: [
          {
            title: 'What is a Satellite?',
            type: 'video',
            content: 'https://example.com/video1',
            duration: 30,
            order: 1,
          },
          {
            title: 'Types of Satellites',
            type: 'text',
            content: 'Detailed explanation of different satellite types...',
            duration: 20,
            order: 2,
          },
        ],
      },
    ],
    prerequisites: ['Basic physics knowledge', 'Mathematics fundamentals'],
    learningOutcomes: ['Understand satellite basics', 'Design simple satellite systems'],
    certification: {
      available: true,
      passingScore: 80,
    },
    pricing: {
      isFree: true,
      price: 0,
    },
    enrollment: {
      totalStudents: 1250,
      isOpen: true,
    },
    ratings: {
      average: 4.8,
      totalRatings: 156,
    },
    isPublished: true,
  },
  {
    title: 'AI Applications in Space Exploration',
    description: 'Explore how artificial intelligence is revolutionizing space missions and data analysis. Learn to implement AI algorithms for space applications.',
    instructor: 'Prof. Kwame Asante',
    instructorId: null,
    category: 'AI in Space',
    level: 'intermediate',
    duration: 35,
    thumbnail: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400',
    tags: ['AI', 'machine learning', 'space exploration', 'data analysis'],
    modules: [
      {
        title: 'AI Fundamentals for Space',
        description: 'Introduction to AI concepts in space context',
        order: 1,
        lessons: [
          {
            title: 'Machine Learning Basics',
            type: 'video',
            content: 'https://example.com/video2',
            duration: 45,
            order: 1,
          },
        ],
      },
    ],
    prerequisites: ['Programming experience', 'Basic mathematics'],
    learningOutcomes: ['Implement AI algorithms', 'Analyze space data'],
    certification: {
      available: true,
      passingScore: 85,
    },
    pricing: {
      isFree: false,
      price: 99,
    },
    enrollment: {
      totalStudents: 890,
      isOpen: true,
    },
    ratings: {
      average: 4.9,
      totalRatings: 89,
    },
    isPublished: true,
  },
  {
    title: 'Cybersecurity for Space Systems',
    description: 'Understand the unique security challenges and solutions for space-based infrastructure. Learn to protect satellite systems from cyber threats.',
    instructor: 'Dr. Fatima Al-Rashid',
    instructorId: null,
    category: 'Cybersecurity',
    level: 'advanced',
    duration: 50,
    thumbnail: 'https://images.unsplash.com/photo-1563206767-5b18f218e8de?w=400',
    tags: ['cybersecurity', 'space systems', 'satellite security'],
    modules: [
      {
        title: 'Space Cybersecurity Fundamentals',
        description: 'Understanding security challenges in space',
        order: 1,
        lessons: [
          {
            title: 'Threat Landscape in Space',
            type: 'video',
            content: 'https://example.com/video3',
            duration: 40,
            order: 1,
          },
        ],
      },
    ],
    prerequisites: ['Cybersecurity basics', 'Network fundamentals'],
    learningOutcomes: ['Secure space systems', 'Implement security protocols'],
    certification: {
      available: true,
      passingScore: 90,
    },
    pricing: {
      isFree: false,
      price: 149,
    },
    enrollment: {
      totalStudents: 650,
      isOpen: true,
    },
    ratings: {
      average: 4.7,
      totalRatings: 67,
    },
    isPublished: true,
  },
];

export const sampleJobs = [
  {
    title: 'Satellite Systems Engineer',
    company: 'Kenya Space Agency',
    employerId: null, // Will be set when we create users
    description: 'Design and develop satellite systems for Earth observation missions. Work with cutting-edge technology to advance Africa\'s space capabilities.',
    requirements: ['Bachelor\'s in Aerospace Engineering', '3+ years experience', 'MATLAB/Simulink proficiency'],
    responsibilities: ['Design satellite subsystems', 'Conduct system analysis', 'Collaborate with international teams'],
    type: 'full-time',
    location: {
      type: 'on-site',
      city: 'Nairobi',
      country: 'Kenya',
    },
    salary: {
      min: 80000,
      max: 120000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Satellite Engineering',
    tags: ['satellite', 'engineering', 'space systems'],
    skillsRequired: ['MATLAB', 'Simulink', 'Systems Engineering'],
    experienceLevel: 'mid',
    education: ['Bachelor\'s degree in Aerospace Engineering'],
    benefits: ['Health insurance', 'Professional development', 'Flexible hours'],
    isActive: true,
    isFeatured: true,
    applications: {
      total: 23,
      shortlisted: 5,
      interviewed: 2,
      hired: 0,
    },
  },
  {
    title: 'AI Research Scientist - Space Applications',
    company: 'Ghana Space Science Institute',
    employerId: null,
    description: 'Develop AI algorithms for satellite data analysis and space mission planning. Lead research initiatives in AI for space exploration.',
    requirements: ['PhD in AI/ML or related field', 'Python/TensorFlow expertise', 'Space domain knowledge'],
    responsibilities: ['Research AI algorithms', 'Publish scientific papers', 'Lead research teams'],
    type: 'full-time',
    location: {
      type: 'remote',
      city: 'Accra',
      country: 'Ghana',
    },
    salary: {
      min: 90000,
      max: 140000,
      currency: 'USD',
      period: 'yearly',
    },
    category: 'Research & Development',
    tags: ['AI', 'machine learning', 'research'],
    skillsRequired: ['Python', 'TensorFlow', 'Research'],
    experienceLevel: 'senior',
    education: ['PhD in AI/ML or related field'],
    benefits: ['Research budget', 'Conference attendance', 'Publication support'],
    isActive: true,
    isFeatured: false,
    applications: {
      total: 45,
      shortlisted: 8,
      interviewed: 3,
      hired: 0,
    },
  },
];

export const sampleUsers = [
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Dr. Amara Okafor',
    role: 'mentor',
    profile: {
      bio: 'Satellite systems engineer with 15 years of experience',
      skills: ['Satellite Engineering', 'Orbital Mechanics', 'Systems Design'],
      interests: ['Space Technology', 'Education'],
      education: 'PhD in Aerospace Engineering',
      experience: '15 years in satellite systems',
      location: 'Nairobi, Kenya',
      currentLevel: 'advanced',
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Nairobi',
    },
  },
  {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Kenya Space Agency HR',
    role: 'employer',
    employment: {
      isEmployer: true,
      company: 'Kenya Space Agency',
      position: 'HR Manager',
    },
    profile: {
      bio: 'Recruiting top talent for space technology roles',
      skills: ['Recruitment', 'HR Management'],
      interests: ['Space Technology', 'Talent Development'],
      location: 'Nairobi, Kenya',
      currentLevel: 'advanced',
    },
    preferences: {
      notifications: {
        email: true,
        sms: true,
        push: true,
      },
      language: 'en',
      timezone: 'Africa/Nairobi',
    },
  },
];

export async function seedDatabase() {
  try {
    await connectDB();

    console.log('Seeding database...');

    // Clear existing data
    await User.deleteMany({});
    await Course.deleteMany({});
    await Job.deleteMany({});

    // Create users first
    const hashedPassword = await bcrypt.hash('password123', 12);
    const users = await User.insertMany(
      sampleUsers.map(user => ({
        ...user,
        password: hashedPassword,
      }))
    );

    // Create courses with instructor IDs
    const coursesWithInstructors = sampleCourses.map((course, index) => ({
      ...course,
      instructorId: users[0]._id, // Assign first user as instructor
    }));

    await Course.insertMany(coursesWithInstructors);

    // Create jobs with employer IDs
    const jobsWithEmployers = sampleJobs.map(job => ({
      ...job,
      employerId: users[1]._id, // Assign second user as employer
    }));

    await Job.insertMany(jobsWithEmployers);

    console.log('Database seeded successfully!');
    console.log('Sample login credentials:');
    console.log('Instructor: <EMAIL> / password123');
    console.log('Employer: <EMAIL> / password123');

  } catch (error) {
    console.error('Error seeding database:', error);
    throw error;
  }
}
